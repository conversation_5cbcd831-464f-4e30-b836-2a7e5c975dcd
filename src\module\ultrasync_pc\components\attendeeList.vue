<template>
    <div class="attendee_list">
        <div v-for="attendee in sortedList" class="attendee_item clearfix" :key="attendee.userid">
            <div class="avatar_box"  @click="openVisitingCard(attendee, 3)">
                <mr-avatar :url="getLocalAvatar(attendee)" :showOnlineState="true" :onlineState="attendee.state" :key="attendee.avatar" :radius="26"></mr-avatar>
            </div>
            <span>{{ attendee.alias_name || remarkMap[attendee.userid] || attendee.nickname }}</span>
            <span v-if="conversation.creator_id == attendee.userid" class="creator_tag">{{
                $t('creator_tag_text')
            }}</span>
            <span v-else-if="attendee.role == systemConfig.groupRole.manager" class="creator_tag">{{
                $t('manager')
            }}</span>
            <span v-else-if="attendee.attendeeState == 2 || attendee.attendeeState == 3" class="creator_tag">{{
                $t('temp_attendee_text')
            }}</span>
            <span v-if="attendee.userid == user.uid" class="me_tag">{{ $t('mine') }}</span>
        </div>
    </div>
</template>
<script>
import base from "../lib/base";
import { getLocalAvatar,openVisitingCard } from "../lib/common_base";
export default {
    mixins: [base],
    name: "AttendeeList",
    props: {
        cid: {
            type: [Number, String],
            default: 0,
        },
        list: {
            type: Array,
            default: () => {
                return [];
            },
        },
        avatarSize:{
            type:Number,
            default:20
        }
    },
    computed: {
        remarkMap() {
            return this.$store.state.friendList.remarkMap;
        },
        conversation() {
            return this.conversationList[this.cid] || {};
        },
        sortedList() {
            // 排序规则：群主 > 管理员 > 我 > 普通成员 > 临时参会者
            const userId = this.user.uid;
            const creatorId = this.conversation.creator_id;
            const managerRole = this.systemConfig.groupRole.manager;
            return [...this.list].sort((a, b) => {
                // 临时参会者始终最后
                const isTempA = a.attendeeState === 2 || a.attendeeState === 3;
                const isTempB = b.attendeeState === 2 || b.attendeeState === 3;
                if (isTempA && !isTempB) {
                    return 1;
                }
                if (!isTempA && isTempB) {
                    return -1;
                }
                // 群主优先
                if (a.userid === creatorId && b.userid !== creatorId) {
                    return -1;
                }
                if (a.userid !== creatorId && b.userid === creatorId) {
                    return 1;
                }
                // 管理员优先
                if (a.role === managerRole && b.role !== managerRole) {
                    return -1;
                }
                if (a.role !== managerRole && b.role === managerRole) {
                    return 1;
                }
                // "我"优先
                if (a.userid === userId && b.userid !== userId) {
                    return -1;
                }
                if (a.userid !== userId && b.userid === userId) {
                    return 1;
                }
                // 其他情况保持原顺序
                return 0;
            });
        }
    },
    data() {
        return {
            getLocalAvatar,
            openVisitingCard
        };
    },
};
</script>
<style scoped lang="scss">
.attendee_list {
    // max-height:200px;
    // min-height:50px;
    // position:relative;
    .__vuescroll {
        .__view {
            width: 100% !important;
        }
    }
    width: 100%;
    height: 100%;
    overflow: auto;
    .attendee_item {
        padding: 5px 10px;
        cursor: pointer;
        display: flex;
        align-items: center;
        .avatar_box {
            vertical-align: top;
            margin-right: 6px;
        }
        & > span {
            vertical-align: top;
            margin-right: 0px;
            max-width: 176px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            margin-right: 10px;
        }
        .creator_tag {
            font-size: 12px;
            background-color: #01c59d;
            color: #fff;
            line-height: 1;
            padding: 2px 4px;
            border-radius: 4px;
            display: inline-block;
            margin-right: 10px;
            overflow: unset;
        }
        .yuying_status {
            width: 18px;
            height: 18px;
            margin-right: 10px;
        }
        .iconbofang {
            color: #01c59d;
            display: inline-block;
            position: relative;
            margin-right: 10px;
            .iconright {
                position: absolute;
                bottom: -4px;
                right: -4px;
                color: #f00;
            }
        }
        .me_tag {
            font-size: 12px;
            background-color: #f5a623;
            color: #fff;
            line-height: 1;
            padding: 2px 4px;
            border-radius: 4px;
            display: inline-block;
            margin-right: 10px;
            overflow: unset;
        }
    }
}
</style>
